/**
 * CryptoFintech App JavaScript
 * Performance-optimized vanilla JavaScript with no dependencies
 * Features: Smooth scroll, intersection observer animations, form validation, pricing toggle
 */

(function() {
    'use strict';

    // Performance optimization: Use passive event listeners where possible
    const supportsPassive = (() => {
        let supportsPassive = false;
        try {
            const opts = Object.defineProperty({}, 'passive', {
                get: function() {
                    supportsPassive = true;
                }
            });
            window.addEventListener('testPassive', null, opts);
            window.removeEventListener('testPassive', null, opts);
        } catch (e) {}
        return supportsPassive;
    })();

    // Utility functions
    const utils = {
        // Debounce function for performance
        debounce: (func, wait) => {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Check if element is in viewport
        isInViewport: (element, threshold = 0.1) => {
            const rect = element.getBoundingClientRect();
            const windowHeight = window.innerHeight || document.documentElement.clientHeight;
            const windowWidth = window.innerWidth || document.documentElement.clientWidth;
            
            return (
                rect.top <= windowHeight * (1 - threshold) &&
                rect.bottom >= windowHeight * threshold &&
                rect.left <= windowWidth * (1 - threshold) &&
                rect.right >= windowWidth * threshold
            );
        },

        // Smooth scroll to element
        scrollToElement: (element, offset = 80) => {
            const elementPosition = element.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - offset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });
        }
    };

    // Navigation functionality
    const navigation = {
        init: () => {
            const navbar = document.getElementById('mainNav');
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');

            // Navbar scroll effect
            const handleScroll = utils.debounce(() => {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            }, 10);

            window.addEventListener('scroll', handleScroll, supportsPassive ? { passive: true } : false);

            // Smooth scroll for navigation links
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    const href = link.getAttribute('href');
                    if (href.startsWith('#')) {
                        e.preventDefault();
                        const target = document.querySelector(href);
                        if (target) {
                            utils.scrollToElement(target);
                            
                            // Close mobile menu if open
                            if (navbarCollapse.classList.contains('show')) {
                                navbarToggler.click();
                            }
                        }
                    }
                });
            });

            // Active link highlighting
            const updateActiveLink = utils.debounce(() => {
                const sections = document.querySelectorAll('section[id]');
                const scrollPos = window.scrollY + 100;

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.offsetHeight;
                    const sectionId = section.getAttribute('id');
                    const correspondingLink = document.querySelector(`.navbar-nav .nav-link[href="#${sectionId}"]`);

                    if (correspondingLink) {
                        if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                            navLinks.forEach(link => link.classList.remove('active'));
                            correspondingLink.classList.add('active');
                        }
                    }
                });
            }, 10);

            window.addEventListener('scroll', updateActiveLink, supportsPassive ? { passive: true } : false);
        }
    };

    // Pricing toggle functionality
    const pricing = {
        init: () => {
            const toggle = document.getElementById('pricingToggle');
            const monthlyPrices = document.querySelectorAll('.amount.monthly');
            const yearlyPrices = document.querySelectorAll('.amount.yearly');

            if (toggle) {
                toggle.addEventListener('change', () => {
                    const isYearly = toggle.checked;
                    
                    monthlyPrices.forEach(price => {
                        price.style.display = isYearly ? 'none' : 'inline';
                    });
                    
                    yearlyPrices.forEach(price => {
                        price.style.display = isYearly ? 'inline' : 'none';
                    });
                });
            }
        }
    };

    // Form validation and submission
    const forms = {
        init: () => {
            const contactForm = document.getElementById('contactForm');
            if (!contactForm) return;

            const formFields = {
                firstName: contactForm.querySelector('#firstName'),
                lastName: contactForm.querySelector('#lastName'),
                email: contactForm.querySelector('#email'),
                message: contactForm.querySelector('#message')
            };

            const validators = {
                required: (value) => value.trim().length > 0,
                email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
                minLength: (value, min) => value.trim().length >= min
            };

            const showError = (field, message) => {
                field.classList.add('is-invalid');
                const feedback = field.parentNode.querySelector('.invalid-feedback');
                if (feedback) {
                    feedback.textContent = message;
                }
            };

            const clearError = (field) => {
                field.classList.remove('is-invalid');
            };

            const validateField = (field, rules) => {
                const value = field.value;
                clearError(field);

                for (const rule of rules) {
                    if (!rule.validator(value, rule.param)) {
                        showError(field, rule.message);
                        return false;
                    }
                }
                return true;
            };

            const fieldRules = {
                firstName: [
                    { validator: validators.required, message: 'First name is required' },
                    { validator: validators.minLength, param: 2, message: 'First name must be at least 2 characters' }
                ],
                lastName: [
                    { validator: validators.required, message: 'Last name is required' },
                    { validator: validators.minLength, param: 2, message: 'Last name must be at least 2 characters' }
                ],
                email: [
                    { validator: validators.required, message: 'Email is required' },
                    { validator: validators.email, message: 'Please enter a valid email address' }
                ],
                message: [
                    { validator: validators.minLength, param: 10, message: 'Message must be at least 10 characters' }
                ]
            };

            // Real-time validation
            Object.keys(formFields).forEach(fieldName => {
                const field = formFields[fieldName];
                if (field) {
                    field.addEventListener('blur', () => {
                        validateField(field, fieldRules[fieldName] || []);
                    });

                    field.addEventListener('input', utils.debounce(() => {
                        if (field.classList.contains('is-invalid')) {
                            validateField(field, fieldRules[fieldName] || []);
                        }
                    }, 300));
                }
            });

            // Form submission
            contactForm.addEventListener('submit', async (e) => {
                e.preventDefault();

                let isValid = true;
                Object.keys(formFields).forEach(fieldName => {
                    const field = formFields[fieldName];
                    if (field && !validateField(field, fieldRules[fieldName] || [])) {
                        isValid = false;
                    }
                });

                if (!isValid) return;

                // Show loading state
                const submitBtn = contactForm.querySelector('button[type="submit"]');
                const btnText = submitBtn.querySelector('.btn-text');
                const btnLoading = submitBtn.querySelector('.btn-loading');
                
                btnText.style.display = 'none';
                btnLoading.style.display = 'flex';
                submitBtn.disabled = true;

                // Simulate form submission (replace with actual API call)
                try {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    // Show success message
                    const successDiv = contactForm.querySelector('.form-success');
                    contactForm.style.display = 'none';
                    successDiv.style.display = 'block';
                    
                } catch (error) {
                    console.error('Form submission error:', error);
                    alert('There was an error sending your message. Please try again.');
                } finally {
                    // Reset button state
                    btnText.style.display = 'inline';
                    btnLoading.style.display = 'none';
                    submitBtn.disabled = false;
                }
            });
        }
    };

    // Intersection Observer for animations
    const animations = {
        init: () => {
            // Check if user prefers reduced motion
            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
            if (prefersReducedMotion) return;

            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            // Animate elements on scroll
            const animateElements = document.querySelectorAll('.feature-card, .pricing-card, .stat-card');
            animateElements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
                observer.observe(el);
            });
        }
    };

    // Performance monitoring
    const performance = {
        init: () => {
            // Log Core Web Vitals if available
            if ('web-vital' in window) {
                import('https://unpkg.com/web-vitals@3/dist/web-vitals.js').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
                    getCLS(console.log);
                    getFID(console.log);
                    getFCP(console.log);
                    getLCP(console.log);
                    getTTFB(console.log);
                });
            }

            // Log page load time
            window.addEventListener('load', () => {
                const loadTime = performance.now();
                console.log(`Page loaded in ${Math.round(loadTime)}ms`);
            });
        }
    };

    // Initialize all modules when DOM is ready
    const init = () => {
        navigation.init();
        pricing.init();
        forms.init();
        animations.init();
        performance.init();
    };

    // DOM ready check
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
