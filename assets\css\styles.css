/* 
CryptoFintech Custom Styles
Performance-optimized CSS with custom variables and <PERSON><PERSON><PERSON> overrides
Dark theme with Gold primary color and complementary accents
*/

/* CSS Variables for consistent theming */
:root {
    /* Primary Colors */
    --primary-gold: #D4AF37;
    --primary-gold-dark: #C9A227;
    --primary-gold-light: #E6C55A;
    
    /* Accent Colors */
    --accent-teal: #00B3A4;
    --accent-teal-dark: #008A7F;
    --accent-slate: #8A8F98;
    --accent-slate-light: #A5AAB5;
    
    /* Background Colors */
    --bg-dark: #0A0B0D;
    --bg-dark-secondary: #1A1B1E;
    --bg-dark-tertiary: #2A2B2E;
    --bg-dark-quaternary: #3A3B3E;
    
    /* Text Colors */
    --text-primary: #FFFFFF;
    --text-secondary: #B8BCC8;
    --text-muted: #6C757D;
    --text-inverse: #0A0B0D;
    
    /* Border & Divider Colors */
    --border-color: #2A2B2E;
    --border-color-light: #3A3B3E;
    --divider-color: #1A1B1E;
    
    /* Shadow Effects */
    --shadow-primary: 0 4px 20px rgba(212, 175, 55, 0.15);
    --shadow-secondary: 0 2px 10px rgba(0, 0, 0, 0.3);
    --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.2);
    --shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.3);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--bg-dark);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: clamp(2rem, 5vw, 3.5rem); }
h2 { font-size: clamp(1.75rem, 4vw, 2.5rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }
h4 { font-size: clamp(1.25rem, 2.5vw, 1.5rem); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

/* Custom Button Styles - Override Bootstrap */
.btn {
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    padding: 0.875rem 2rem;
    transition: all var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: 0.95rem;
    line-height: 1;
}

.btn-primary-custom {
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
    color: var(--text-inverse);
    box-shadow: var(--shadow-secondary);
}

.btn-primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-primary);
    color: var(--text-inverse);
    background: linear-gradient(135deg, var(--primary-gold-light) 0%, var(--primary-gold) 100%);
}

.btn-outline-custom {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color-light);
}

.btn-outline-custom:hover {
    background: var(--bg-dark-tertiary);
    border-color: var(--primary-gold);
    color: var(--primary-gold);
    transform: translateY(-2px);
}

/* Navigation Styles */
.navbar-custom {
    background: rgba(10, 11, 13, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
    transition: all var(--transition-normal);
}

.navbar-custom.scrolled {
    background: rgba(10, 11, 13, 0.98);
    padding: var(--spacing-sm) 0;
}

.navbar-brand {
    color: var(--text-primary) !important;
    font-weight: 700;
    font-size: 1.25rem;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.navbar-nav .nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    transition: color var(--transition-normal);
    position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-gold) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-gold);
    transition: all var(--transition-normal);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 80%;
}

.navbar-toggler {
    border: none;
    padding: var(--spacing-xs);
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-dark-secondary) 100%);
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding-top: 80px;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 20%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(0, 179, 164, 0.08) 0%, transparent 50%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gold {
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
}

.hero-cta {
    margin-bottom: var(--spacing-xxl);
}

.hero-stats {
    opacity: 0;
    animation: fadeInUp 1s ease 0.8s forwards;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-gold);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    animation: fadeInRight 1s ease 0.6s forwards;
}

.trading-card {
    background: var(--bg-dark-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-card);
    max-width: 400px;
    width: 100%;
}

.trading-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.trading-card h6 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
}

.profit-indicator {
    background: var(--accent-teal);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 600;
}

.chart-placeholder {
    height: 200px;
    border-radius: var(--radius-md);
    overflow: hidden;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Particle Animation */
.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.hero-particles::before,
.hero-particles::after {
    content: '';
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-gold);
    border-radius: 50%;
    opacity: 0.6;
    animation: float 6s ease-in-out infinite;
}

.hero-particles::before {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.hero-particles::after {
    top: 60%;
    right: 15%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* Section Styles */
.section-title {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.section-subtitle {
    color: var(--text-secondary);
    font-size: 1.125rem;
    text-align: center;
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.section-text {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.7;
}

/* Features Section */
.features-section {
    background: var(--bg-dark-secondary);
    padding: var(--spacing-xxl) 0;
}

.feature-card {
    background: var(--bg-dark);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-normal);
    height: 100%;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold) 0%, var(--accent-teal) 100%);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-gold);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-icon {
    margin-bottom: var(--spacing-lg);
    display: flex;
    justify-content: center;
}

.feature-card h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.feature-card p {
    color: var(--text-secondary);
    margin-bottom: 0;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding-top: 100px;
        text-align: center;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
    
    .hero-cta .btn {
        display: block;
        margin-bottom: var(--spacing-md);
        width: 100%;
    }
    
    .hero-cta .btn:last-child {
        margin-bottom: 0;
    }
    
    .trading-card {
        margin-top: var(--spacing-xl);
    }
    
    .feature-card {
        margin-bottom: var(--spacing-lg);
    }
}

/* Pricing Section */
.pricing-section {
    background: var(--bg-dark);
    padding: var(--spacing-xxl) 0;
}

.pricing-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.toggle-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-dark-tertiary);
    transition: var(--transition-normal);
    border-radius: 30px;
    border: 1px solid var(--border-color);
}

.slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 3px;
    bottom: 3px;
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
    transition: var(--transition-normal);
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--bg-dark-tertiary);
    border-color: var(--primary-gold);
}

input:checked + .slider:before {
    transform: translateX(30px);
}

.badge {
    background: var(--accent-teal);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: var(--spacing-sm);
}

.pricing-card {
    background: var(--bg-dark-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-normal);
    height: 100%;
    position: relative;
    overflow: hidden;
}

.pricing-card.featured {
    border-color: var(--primary-gold);
    transform: scale(1.05);
    box-shadow: var(--shadow-primary);
}

.popular-badge {
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
    color: var(--text-inverse);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: 0 0 var(--radius-md) var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
}

.pricing-header {
    margin-bottom: var(--spacing-xl);
}

.pricing-header h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: 700;
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: var(--spacing-sm);
}

.currency {
    font-size: 1.25rem;
    color: var(--primary-gold);
    font-weight: 600;
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 var(--spacing-xs);
}

.period {
    font-size: 1rem;
    color: var(--text-muted);
}

.pricing-header p {
    color: var(--text-secondary);
    margin-bottom: 0;
}

.pricing-features {
    list-style: none;
    margin-bottom: var(--spacing-xl);
    text-align: left;
}

.pricing-features li {
    padding: var(--spacing-sm) 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: var(--spacing-lg);
}

.pricing-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-teal);
    font-weight: bold;
}

/* About Section */
.about-section {
    background: var(--bg-dark-secondary);
    padding: var(--spacing-xxl) 0;
}

.trust-badges {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.badge-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

.stat-card {
    background: var(--bg-dark);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-card);
    border-color: var(--primary-gold);
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-gold);
    margin-bottom: var(--spacing-sm);
}

.stat-card .stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Contact Section */
.contact-section {
    background: var(--bg-dark);
    padding: var(--spacing-xxl) 0;
}

.contact-form {
    background: var(--bg-dark-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-card);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
}

.form-control {
    width: 100%;
    padding: 0.875rem var(--spacing-md);
    background: var(--bg-dark);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all var(--transition-normal);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
    background: var(--bg-dark-tertiary);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.invalid-feedback {
    display: none;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: var(--spacing-xs);
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.form-control.is-invalid ~ .invalid-feedback {
    display: block;
}

.form-success {
    background: var(--bg-dark-tertiary);
    border: 1px solid var(--accent-teal);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.success-message {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--accent-teal);
    font-weight: 500;
}

/* Footer Section */
.footer-section {
    background: var(--bg-dark-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-xxl) 0 var(--spacing-xl) 0;
}

.footer-brand {
    margin-bottom: var(--spacing-lg);
}

.footer-logo {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 700;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
}

.footer-description {
    color: var(--text-secondary);
    margin-bottom: 0;
    max-width: 300px;
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--bg-dark);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    transition: all var(--transition-normal);
    text-decoration: none;
}

.social-links a:hover {
    background: var(--primary-gold);
    color: var(--text-inverse);
    transform: translateY(-2px);
}

.footer-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    font-size: 1rem;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.footer-links a:hover {
    color: var(--primary-gold);
}

.footer-divider {
    border: none;
    height: 1px;
    background: var(--border-color);
    margin: var(--spacing-xl) 0 var(--spacing-lg) 0;
}

.footer-copyright,
.footer-disclaimer {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-bottom: 0;
}

@media (max-width: 576px) {
    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    .navbar-custom {
        padding: var(--spacing-sm) 0;
    }

    .hero-section {
        min-height: 90vh;
    }

    .stat-number {
        font-size: 1.25rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .pricing-card.featured {
        transform: none;
        margin-bottom: var(--spacing-lg);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .trust-badges {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .social-links {
        justify-content: center;
    }

    .footer-copyright,
    .footer-disclaimer {
        text-align: center;
    }
}
