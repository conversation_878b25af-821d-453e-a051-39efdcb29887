<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1200" height="630" fill="#0A0B0D"/>
  
  <!-- Gradient overlay -->
  <defs>
    <radialGradient id="ogGradient" cx="50%" cy="50%" r="60%">
      <stop offset="0%" style="stop-color:#D4AF37;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:0" />
    </radialGradient>
  </defs>
  <rect width="1200" height="630" fill="url(#ogGradient)"/>
  
  <!-- Logo -->
  <g transform="translate(100, 200)">
    <rect width="80" height="80" rx="16" fill="url(#logoGradient)"/>
    <path d="M40 16L56 32L40 48L24 32L40 16Z" fill="white"/>
    <path d="M40 32L56 48L40 64L24 48L40 32Z" fill="white" opacity="0.7"/>
    <defs>
      <linearGradient id="logoGradient" x1="0" y1="0" x2="80" y2="80" gradientUnits="userSpaceOnUse">
        <stop stop-color="#D4AF37"/>
        <stop offset="1" stop-color="#C9A227"/>
      </linearGradient>
    </defs>
  </g>
  
  <!-- Text -->
  <text x="220" y="250" fill="#FFFFFF" font-family="Arial, sans-serif" font-size="48" font-weight="bold">CryptoFintech</text>
  <text x="220" y="290" fill="#B8BCC8" font-family="Arial, sans-serif" font-size="24">Advanced Crypto Trading Platform</text>
  <text x="220" y="330" fill="#8A8F98" font-family="Arial, sans-serif" font-size="18">Professional trading with institutional-grade tools</text>
  
  <!-- Decorative elements -->
  <circle cx="1000" cy="150" r="60" fill="#D4AF37" opacity="0.1"/>
  <circle cx="1050" cy="400" r="40" fill="#00B3A4" opacity="0.1"/>
  <polygon points="900,500 950,450 1000,500 950,550" fill="#8A8F98" opacity="0.05"/>
</svg>
