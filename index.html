<!DOCTYPE html>
<html lang="en">
<head>
    <!-- 
    PERFORMANCE OPTIMIZATION NOTES:
    - Critical CSS inlined for above-the-fold content
    - Non-critical CSS loaded deferred
    - All JS deferred to avoid render blocking
    - Preload hero image with modern formats (WebP/AVIF)
    - System font stack for speed, display font preloaded with font-display: swap
    - Minimal DOM structure for faster parsing
    - CSS variables for efficient styling
    - No jQuery or heavy dependencies
    - Optimized for Core Web Vitals and Lighthouse 95+ scores
    -->
    
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Advanced crypto trading platform with institutional-grade tools, real-time analytics, and secure portfolio management. Start trading with CryptoFintech today.">
    <meta name="keywords" content="crypto trading, cryptocurrency, bitcoin, ethereum, trading platform, portfolio management, fintech">
    <meta name="author" content="CryptoFintech">
    
    <!-- Open Graph / Social Media -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="CryptoFintech - Advanced Crypto Trading Platform">
    <meta property="og:description" content="Professional crypto trading with institutional-grade tools and real-time analytics">
    <meta property="og:url" content="https://cryptofintech.com">
    <meta property="og:image" content="https://cryptofintech.com/assets/img/og-image.svg">

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="CryptoFintech - Advanced Crypto Trading Platform">
    <meta name="twitter:description" content="Professional crypto trading with institutional-grade tools and real-time analytics">
    <meta name="twitter:image" content="https://cryptofintech.com/assets/img/og-image.svg">
    
    <title>CryptoFintech - Advanced Crypto Trading Platform</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="assets/img/hero-bg.svg" as="image" type="image/svg+xml">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">

    <!-- Manifest for PWA -->
    <link rel="manifest" href="manifest.json">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/img/favicon.svg">
    <link rel="icon" type="image/png" href="assets/img/favicon.png">
    
    <!-- Bootstrap 5 CSS (only for grid/utilities) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous">
    
    <!-- Critical CSS inlined -->
    <style>
        :root {
            --primary-gold: #D4AF37;
            --primary-gold-dark: #C9A227;
            --accent-teal: #00B3A4;
            --accent-slate: #8A8F98;
            --bg-dark: #0A0B0D;
            --bg-dark-secondary: #1A1B1E;
            --bg-dark-tertiary: #2A2B2E;
            --text-primary: #FFFFFF;
            --text-secondary: #B8BCC8;
            --text-muted: #6C757D;
            --border-color: #2A2B2E;
            --shadow-primary: 0 4px 20px rgba(212, 175, 55, 0.15);
            --shadow-secondary: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--bg-dark);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        /* Critical above-the-fold styles */
        .navbar-custom {
            background: rgba(10, 11, 13, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
        }
        
        .hero-section {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-dark-secondary) 100%);
            position: relative;
            display: flex;
            align-items: center;
            overflow: hidden;
        }
        
        .hero-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('assets/img/hero-bg.svg');
            background-size: cover;
            background-position: center;
            opacity: 0.8;
            z-index: 1;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .btn-primary-custom {
            background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
            border: none;
            color: var(--bg-dark);
            font-weight: 600;
            padding: 0.875rem 2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-primary);
            color: var(--bg-dark);
        }
    </style>
    
    <!-- Non-critical CSS loaded deferred -->
    <link rel="stylesheet" href="assets/css/styles.css" media="print" onload="this.media='all'">
    <noscript><link rel="stylesheet" href="assets/css/styles.css"></noscript>
</head>
<body>
    <!-- Header/Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top" id="mainNav">
        <div class="container">
            <a class="navbar-brand" href="#home">
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="40" height="40" rx="8" fill="url(#logoGradient)"/>
                    <path d="M20 8L28 16L20 24L12 16L20 8Z" fill="white"/>
                    <path d="M20 16L28 24L20 32L12 24L20 16Z" fill="white" opacity="0.7"/>
                    <defs>
                        <linearGradient id="logoGradient" x1="0" y1="0" x2="40" y2="40" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#D4AF37"/>
                            <stop offset="1" stop-color="#C9A227"/>
                        </linearGradient>
                    </defs>
                </svg>
                <span class="ms-2 fw-bold">CryptoFintech</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item"><a class="nav-link" href="#home">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="#features">Features</a></li>
                    <li class="nav-item"><a class="nav-link" href="#pricing">Pricing</a></li>
                    <li class="nav-item"><a class="nav-link" href="#about">About</a></li>
                    <li class="nav-item"><a class="nav-link" href="#contact">Contact</a></li>
                </ul>
                <a href="#contact" class="btn btn-primary-custom">Get Started</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="hero-bg"></div>
        <div class="container hero-content">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="hero-title mb-4">
                        Trade Crypto Like a <span class="text-gold">Professional</span>
                    </h1>
                    <p class="hero-subtitle mb-5">
                        Advanced trading platform with institutional-grade tools, real-time analytics, 
                        and secure portfolio management. Join thousands of traders maximizing their potential.
                    </p>
                    <div class="hero-cta">
                        <a href="#contact" class="btn btn-primary-custom me-3">Start Trading</a>
                        <a href="#features" class="btn btn-outline-custom">Learn More</a>
                    </div>
                    <div class="hero-stats mt-5">
                        <div class="row">
                            <div class="col-4">
                                <div class="stat-item">
                                    <div class="stat-number">$2.5B+</div>
                                    <div class="stat-label">Volume Traded</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <div class="stat-number">50K+</div>
                                    <div class="stat-label">Active Traders</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <div class="stat-number">99.9%</div>
                                    <div class="stat-label">Uptime</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-visual">
                        <div class="trading-card">
                            <div class="card-header">
                                <h6>Portfolio Overview</h6>
                                <span class="profit-indicator">+24.5%</span>
                            </div>
                            <div class="chart-placeholder">
                                <svg width="100%" height="200" viewBox="0 0 400 200">
                                    <defs>
                                        <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#D4AF37;stop-opacity:0.3" />
                                            <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:0" />
                                        </linearGradient>
                                    </defs>
                                    <path d="M0,150 Q100,120 200,80 T400,60" stroke="#D4AF37" stroke-width="3" fill="none"/>
                                    <path d="M0,150 Q100,120 200,80 T400,60 L400,200 L0,200 Z" fill="url(#chartGradient)"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="hero-particles"></div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="section-title">Powerful Trading Features</h2>
                    <p class="section-subtitle">Everything you need to trade crypto professionally</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                                <rect width="48" height="48" rx="12" fill="var(--primary-gold)" opacity="0.1"/>
                                <path d="M24 12L32 20L24 28L16 20L24 12Z" fill="var(--primary-gold)"/>
                                <path d="M24 20L32 28L24 36L16 28L24 20Z" fill="var(--primary-gold)" opacity="0.6"/>
                            </svg>
                        </div>
                        <h4>Advanced Analytics</h4>
                        <p>Real-time market data, technical indicators, and AI-powered insights to make informed trading decisions.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                                <rect width="48" height="48" rx="12" fill="var(--accent-teal)" opacity="0.1"/>
                                <path d="M14 18H34V30H14V18Z" stroke="var(--accent-teal)" stroke-width="2" fill="none"/>
                                <path d="M18 22H30M18 26H26" stroke="var(--accent-teal)" stroke-width="2"/>
                                <circle cx="24" cy="14" r="3" fill="var(--accent-teal)"/>
                            </svg>
                        </div>
                        <h4>Secure Wallet</h4>
                        <p>Multi-signature security, cold storage integration, and insurance coverage for your digital assets.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                                <rect width="48" height="48" rx="12" fill="var(--primary-gold)" opacity="0.1"/>
                                <path d="M12 24L20 16L28 24L36 16" stroke="var(--primary-gold)" stroke-width="3" fill="none"/>
                                <path d="M12 32L20 24L28 32L36 24" stroke="var(--primary-gold)" stroke-width="2" opacity="0.6" fill="none"/>
                            </svg>
                        </div>
                        <h4>Automated Trading</h4>
                        <p>Set up trading bots, stop-loss orders, and automated strategies to trade 24/7 without missing opportunities.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                                <rect width="48" height="48" rx="12" fill="var(--accent-teal)" opacity="0.1"/>
                                <circle cx="24" cy="24" r="8" stroke="var(--accent-teal)" stroke-width="2" fill="none"/>
                                <path d="M24 16V24L28 28" stroke="var(--accent-teal)" stroke-width="2"/>
                            </svg>
                        </div>
                        <h4>Real-time Execution</h4>
                        <p>Lightning-fast order execution with sub-millisecond latency and direct market access.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                                <rect width="48" height="48" rx="12" fill="var(--primary-gold)" opacity="0.1"/>
                                <path d="M16 20H32V32H16V20Z" stroke="var(--primary-gold)" stroke-width="2" fill="none"/>
                                <path d="M20 16V20M28 16V20" stroke="var(--primary-gold)" stroke-width="2"/>
                                <path d="M20 24H28M20 28H24" stroke="var(--primary-gold)" stroke-width="2"/>
                            </svg>
                        </div>
                        <h4>Portfolio Management</h4>
                        <p>Track performance, rebalance automatically, and get detailed reports on your crypto investments.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                                <rect width="48" height="48" rx="12" fill="var(--accent-teal)" opacity="0.1"/>
                                <path d="M24 12C30.6274 12 36 17.3726 36 24C36 30.6274 30.6274 36 24 36C17.3726 36 12 30.6274 12 24C12 17.3726 17.3726 12 24 12Z" stroke="var(--accent-teal)" stroke-width="2" fill="none"/>
                                <path d="M24 18V24L28 28" stroke="var(--accent-teal)" stroke-width="2"/>
                            </svg>
                        </div>
                        <h4>24/7 Support</h4>
                        <p>Round-the-clock customer support with dedicated account managers for premium users.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="section-title">Choose Your Plan</h2>
                    <p class="section-subtitle">Flexible pricing for traders of all levels</p>
                    <div class="pricing-toggle mt-4">
                        <span class="toggle-label">Monthly</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="pricingToggle">
                            <span class="slider"></span>
                        </label>
                        <span class="toggle-label">Yearly <span class="badge">Save 20%</span></span>
                    </div>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4">
                    <div class="pricing-card">
                        <div class="pricing-header">
                            <h4>Starter</h4>
                            <div class="price">
                                <span class="currency">$</span>
                                <span class="amount monthly">29</span>
                                <span class="amount yearly" style="display: none;">23</span>
                                <span class="period">/month</span>
                            </div>
                            <p>Perfect for beginners</p>
                        </div>
                        <ul class="pricing-features">
                            <li>Basic trading tools</li>
                            <li>5 cryptocurrencies</li>
                            <li>Email support</li>
                            <li>Mobile app access</li>
                            <li>Basic analytics</li>
                        </ul>
                        <a href="#contact" class="btn btn-outline-custom w-100">Get Started</a>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="pricing-card featured">
                        <div class="popular-badge">Most Popular</div>
                        <div class="pricing-header">
                            <h4>Pro</h4>
                            <div class="price">
                                <span class="currency">$</span>
                                <span class="amount monthly">99</span>
                                <span class="amount yearly" style="display: none;">79</span>
                                <span class="period">/month</span>
                            </div>
                            <p>For serious traders</p>
                        </div>
                        <ul class="pricing-features">
                            <li>Advanced trading tools</li>
                            <li>50+ cryptocurrencies</li>
                            <li>Priority support</li>
                            <li>API access</li>
                            <li>Advanced analytics</li>
                            <li>Automated trading</li>
                            <li>Portfolio management</li>
                        </ul>
                        <a href="#contact" class="btn btn-primary-custom w-100">Start Free Trial</a>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="pricing-card">
                        <div class="pricing-header">
                            <h4>Enterprise</h4>
                            <div class="price">
                                <span class="currency">$</span>
                                <span class="amount monthly">299</span>
                                <span class="amount yearly" style="display: none;">239</span>
                                <span class="period">/month</span>
                            </div>
                            <p>For institutions</p>
                        </div>
                        <ul class="pricing-features">
                            <li>All Pro features</li>
                            <li>Unlimited cryptocurrencies</li>
                            <li>Dedicated support</li>
                            <li>Custom integrations</li>
                            <li>White-label solutions</li>
                            <li>Compliance tools</li>
                            <li>Custom reporting</li>
                        </ul>
                        <a href="#contact" class="btn btn-outline-custom w-100">Contact Sales</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="section-title">Trusted by Professionals Worldwide</h2>
                    <p class="section-text mb-4">
                        CryptoFintech is built by traders, for traders. Our platform combines cutting-edge technology
                        with institutional-grade security to provide the most advanced crypto trading experience.
                    </p>
                    <p class="section-text mb-4">
                        With over 5 years in the market, we've processed billions in trading volume and earned the
                        trust of thousands of professional traders and institutions worldwide.
                    </p>
                    <div class="trust-badges">
                        <div class="badge-item">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="var(--primary-gold)"/>
                            </svg>
                            <span>SOC 2 Certified</span>
                        </div>
                        <div class="badge-item">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="var(--accent-teal)"/>
                            </svg>
                            <span>Bank-Grade Security</span>
                        </div>
                        <div class="badge-item">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="10" stroke="var(--primary-gold)" stroke-width="2" fill="none"/>
                                <path d="M8 12L11 15L16 9" stroke="var(--primary-gold)" stroke-width="2"/>
                            </svg>
                            <span>Regulatory Compliant</span>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="about-visual">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">$2.5B+</div>
                                <div class="stat-label">Trading Volume</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">50K+</div>
                                <div class="stat-label">Active Users</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">99.9%</div>
                                <div class="stat-label">Uptime</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">150+</div>
                                <div class="stat-label">Countries</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="section-title">Get Started Today</h2>
                    <p class="section-subtitle">Join thousands of traders already using CryptoFintech</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <form id="contactForm" class="contact-form">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="firstName">First Name</label>
                                    <input type="text" id="firstName" name="firstName" class="form-control" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="lastName">Last Name</label>
                                    <input type="text" id="lastName" name="lastName" class="form-control" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="email">Email Address</label>
                                    <input type="email" id="email" name="email" class="form-control" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="message">Message</label>
                                    <textarea id="message" name="message" class="form-control" rows="5" placeholder="Tell us about your trading needs..."></textarea>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary-custom w-100">
                                    <span class="btn-text">Send Message</span>
                                    <span class="btn-loading" style="display: none;">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <circle cx="12" cy="12" r="3" fill="currentColor">
                                                <animate attributeName="r" values="3;6;3" dur="1s" repeatCount="indefinite"/>
                                                <animate attributeName="opacity" values="1;0.5;1" dur="1s" repeatCount="indefinite"/>
                                            </circle>
                                        </svg>
                                        Sending...
                                    </span>
                                </button>
                            </div>
                        </div>
                        <div class="form-success" style="display: none;">
                            <div class="success-message">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <circle cx="12" cy="12" r="10" fill="var(--accent-teal)"/>
                                    <path d="M8 12L11 15L16 9" stroke="white" stroke-width="2"/>
                                </svg>
                                <span>Thank you! We'll get back to you within 24 hours.</span>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer-section py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <div class="footer-brand">
                        <a href="#home" class="footer-logo">
                            <svg width="32" height="32" viewBox="0 0 40 40" fill="none">
                                <rect width="40" height="40" rx="8" fill="url(#footerLogoGradient)"/>
                                <path d="M20 8L28 16L20 24L12 16L20 8Z" fill="white"/>
                                <path d="M20 16L28 24L20 32L12 24L20 16Z" fill="white" opacity="0.7"/>
                                <defs>
                                    <linearGradient id="footerLogoGradient" x1="0" y1="0" x2="40" y2="40">
                                        <stop stop-color="#D4AF37"/>
                                        <stop offset="1" stop-color="#C9A227"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                            <span class="ms-2">CryptoFintech</span>
                        </a>
                        <p class="footer-description mt-3">
                            Professional crypto trading platform with institutional-grade tools and security.
                        </p>
                        <div class="social-links mt-4">
                            <a href="#" aria-label="Twitter">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M23 3a10.9 10.9 0 01-3.14 1.53 4.48 4.48 0 00-7.86 3v1A10.66 10.66 0 013 4s-4 9 5 13a11.64 11.64 0 01-7 2c9 5 20 0 20-11.5a4.5 4.5 0 00-.08-.83A7.72 7.72 0 0023 3z"/>
                                </svg>
                            </a>
                            <a href="#" aria-label="LinkedIn">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M16 8a6 6 0 016 6v7h-4v-7a2 2 0 00-2-2 2 2 0 00-2 2v7h-4v-7a6 6 0 016-6zM2 9h4v12H2z"/>
                                    <circle cx="4" cy="4" r="2"/>
                                </svg>
                            </a>
                            <a href="#" aria-label="Telegram">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M11.944 0A12 12 0 000 12a12 12 0 0012 12 12 12 0 0012-12A12 12 0 0011.944 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 01.171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h6 class="footer-title">Platform</h6>
                    <ul class="footer-links">
                        <li><a href="#features">Features</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="#">API</a></li>
                        <li><a href="#">Mobile App</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h6 class="footer-title">Company</h6>
                    <ul class="footer-links">
                        <li><a href="#about">About</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Press</a></li>
                        <li><a href="#">Blog</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h6 class="footer-title">Support</h6>
                    <ul class="footer-links">
                        <li><a href="#contact">Contact</a></li>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Status</a></li>
                        <li><a href="#">Security</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h6 class="footer-title">Legal</h6>
                    <ul class="footer-links">
                        <li><a href="#">Privacy</a></li>
                        <li><a href="#">Terms</a></li>
                        <li><a href="#">Compliance</a></li>
                        <li><a href="#">Licenses</a></li>
                    </ul>
                </div>
            </div>
            <hr class="footer-divider">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="footer-copyright">© 2024 CryptoFintech. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="footer-disclaimer">
                        Trading involves risk. Past performance is not indicative of future results.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS (deferred) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" crossorigin="anonymous" defer></script>

    <!-- Custom JS (deferred) -->
    <script src="assets/js/app.js" defer></script>
</body>
</html>
