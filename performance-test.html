<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoFintech Performance Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #0A0B0D;
            color: #FFFFFF;
        }
        .metric {
            background: #1A1B1E;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #D4AF37;
        }
        .good { border-left-color: #00B3A4; }
        .needs-improvement { border-left-color: #FFA500; }
        .poor { border-left-color: #FF4444; }
        .score {
            font-size: 24px;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>CryptoFintech Performance Metrics</h1>
    <p>This page tests the performance of the main CryptoFintech website.</p>
    
    <div id="metrics">
        <div class="metric">
            <span class="score" id="lcp-score">-</span>
            <strong>Largest Contentful Paint (LCP):</strong> <span id="lcp-value">Measuring...</span>
            <br><small>Good: ≤2.5s, Needs Improvement: ≤4.0s, Poor: >4.0s</small>
        </div>
        
        <div class="metric">
            <span class="score" id="fid-score">-</span>
            <strong>First Input Delay (FID):</strong> <span id="fid-value">Measuring...</span>
            <br><small>Good: ≤100ms, Needs Improvement: ≤300ms, Poor: >300ms</small>
        </div>
        
        <div class="metric">
            <span class="score" id="cls-score">-</span>
            <strong>Cumulative Layout Shift (CLS):</strong> <span id="cls-value">Measuring...</span>
            <br><small>Good: ≤0.1, Needs Improvement: ≤0.25, Poor: >0.25</small>
        </div>
        
        <div class="metric">
            <span class="score" id="fcp-score">-</span>
            <strong>First Contentful Paint (FCP):</strong> <span id="fcp-value">Measuring...</span>
            <br><small>Good: ≤1.8s, Needs Improvement: ≤3.0s, Poor: >3.0s</small>
        </div>
        
        <div class="metric">
            <span class="score" id="ttfb-score">-</span>
            <strong>Time to First Byte (TTFB):</strong> <span id="ttfb-value">Measuring...</span>
            <br><small>Good: ≤800ms, Needs Improvement: ≤1800ms, Poor: >1800ms</small>
        </div>
    </div>
    
    <div style="margin-top: 30px;">
        <h2>Test Instructions:</h2>
        <ol>
            <li>Open the main <a href="index.html" style="color: #D4AF37;">CryptoFintech website</a></li>
            <li>Open browser DevTools (F12)</li>
            <li>Go to Lighthouse tab</li>
            <li>Run audit for Performance, Accessibility, Best Practices, and SEO</li>
            <li>Target: All scores should be 95+ on mobile</li>
        </ol>
    </div>

    <script type="module">
        // Web Vitals measurement
        async function measureWebVitals() {
            try {
                const { getCLS, getFID, getFCP, getLCP, getTTFB } = await import('https://unpkg.com/web-vitals@3/dist/web-vitals.js');
                
                const updateMetric = (name, value, thresholds) => {
                    const scoreEl = document.getElementById(`${name.toLowerCase()}-score`);
                    const valueEl = document.getElementById(`${name.toLowerCase()}-value`);
                    const metricEl = scoreEl.parentElement;
                    
                    valueEl.textContent = `${Math.round(value * 1000) / 1000}${name === 'CLS' ? '' : 'ms'}`;
                    
                    let rating = 'good';
                    if (value > thresholds.poor) rating = 'poor';
                    else if (value > thresholds.needsImprovement) rating = 'needs-improvement';
                    
                    metricEl.className = `metric ${rating}`;
                    scoreEl.textContent = rating === 'good' ? '✓' : rating === 'needs-improvement' ? '⚠' : '✗';
                };
                
                getCLS((metric) => updateMetric('CLS', metric.value, { needsImprovement: 0.1, poor: 0.25 }));
                getFID((metric) => updateMetric('FID', metric.value, { needsImprovement: 100, poor: 300 }));
                getFCP((metric) => updateMetric('FCP', metric.value, { needsImprovement: 1800, poor: 3000 }));
                getLCP((metric) => updateMetric('LCP', metric.value, { needsImprovement: 2500, poor: 4000 }));
                getTTFB((metric) => updateMetric('TTFB', metric.value, { needsImprovement: 800, poor: 1800 }));
                
            } catch (error) {
                console.log('Web Vitals not available:', error);
                document.getElementById('metrics').innerHTML += '<div class="metric"><strong>Note:</strong> Web Vitals measurement requires the main site to be loaded first.</div>';
            }
        }
        
        measureWebVitals();
    </script>
</body>
</html>
