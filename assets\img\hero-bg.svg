<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="heroGradient1" cx="30%" cy="20%" r="40%">
      <stop offset="0%" style="stop-color:#D4AF37;stop-opacity:0.15" />
      <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:0" />
    </radialGradient>
    <radialGradient id="heroGradient2" cx="70%" cy="80%" r="35%">
      <stop offset="0%" style="stop-color:#00B3A4;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#00B3A4;stop-opacity:0" />
    </radialGradient>
    <pattern id="noisePattern" patternUnits="userSpaceOnUse" width="100" height="100">
      <circle cx="20" cy="20" r="1" fill="#D4AF37" opacity="0.1"/>
      <circle cx="80" cy="40" r="0.5" fill="#00B3A4" opacity="0.08"/>
      <circle cx="50" cy="70" r="1.5" fill="#8A8F98" opacity="0.05"/>
      <circle cx="10" cy="90" r="0.8" fill="#D4AF37" opacity="0.06"/>
      <circle cx="90" cy="10" r="1.2" fill="#00B3A4" opacity="0.07"/>
    </pattern>
  </defs>
  
  <!-- Background gradients -->
  <rect width="1920" height="1080" fill="url(#heroGradient1)"/>
  <rect width="1920" height="1080" fill="url(#heroGradient2)"/>
  
  <!-- Subtle noise pattern -->
  <rect width="1920" height="1080" fill="url(#noisePattern)" opacity="0.3"/>
  
  <!-- Geometric shapes for visual interest -->
  <g opacity="0.05">
    <polygon points="200,100 400,200 300,400 100,300" fill="#D4AF37"/>
    <polygon points="1500,200 1700,100 1800,300 1600,400" fill="#00B3A4"/>
    <polygon points="800,600 1000,500 1100,700 900,800" fill="#8A8F98"/>
  </g>
  
  <!-- Subtle grid lines -->
  <g stroke="#D4AF37" stroke-width="0.5" opacity="0.03">
    <line x1="0" y1="270" x2="1920" y2="270"/>
    <line x1="0" y1="540" x2="1920" y2="540"/>
    <line x1="0" y1="810" x2="1920" y2="810"/>
    <line x1="480" y1="0" x2="480" y2="1080"/>
    <line x1="960" y1="0" x2="960" y2="1080"/>
    <line x1="1440" y1="0" x2="1440" y2="1080"/>
  </g>
</svg>
