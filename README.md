# CryptoFintech - Advanced Crypto Trading Platform

A high-performance, dark-themed crypto fintech website built with HTML5, CSS3, JavaScript, and Bootstrap 5.

## Features

- **Dark Theme with Gold Accents**: Professional color scheme with #D4AF37 gold primary and complementary teal/slate accents
- **Performance Optimized**: Built for Lighthouse 95+ scores with critical CSS inlining, deferred loading, and minimal dependencies
- **Fully Responsive**: Mobile-first design that works flawlessly on all devices
- **Accessibility Compliant**: WCAG AA+ compliant with semantic HTML, ARIA labels, and proper focus management
- **Modern Animations**: Lightweight CSS/JS animations with reduced motion support
- **Custom Bootstrap Override**: No default Bootstrap styling - completely custom design system

## Performance Optimizations

- Critical CSS inlined for above-the-fold content
- Non-critical CSS and JS deferred
- SVG graphics for scalability and performance
- System font stack for speed
- Minimal DOM structure
- Passive event listeners
- Debounced scroll handlers
- Intersection Observer for animations
- No jQuery or heavy dependencies

## Sections

1. **Header/Navigation**: Sticky navigation with smooth scroll and active link highlighting
2. **Hero**: Bold headline with animated background and trading card visual
3. **Features**: 6 feature cards with custom SVG icons and hover effects
4. **Pricing**: 3-tier pricing with monthly/yearly toggle
5. **About**: Trust badges and statistics grid
6. **Contact**: Form with real-time validation and success states
7. **Footer**: Comprehensive links and social media

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## File Structure

```
├── index.html              # Main HTML file
├── assets/
│   ├── css/
│   │   └── styles.css      # Custom CSS with variables
│   ├── js/
│   │   └── app.js          # Vanilla JavaScript functionality
│   └── img/
│       ├── hero-bg.svg     # Hero background graphic
│       └── favicon.svg     # Site favicon
├── manifest.json           # PWA manifest
├── robots.txt             # SEO robots file
├── sitemap.xml            # XML sitemap
└── README.md              # This file
```

## Development

Simply open `index.html` in a modern browser or serve via a local HTTP server for best results.

For production deployment, consider:
- Minifying CSS/JS files
- Enabling gzip compression
- Setting up proper caching headers
- Adding a CDN for static assets

## License

© 2024 CryptoFintech. All rights reserved.
